{php $control['form']->action .= "#frm-newsletterForm-form"}

{snippet form}
	{form form class: 'f-newsletter block-loader', data-naja: '', novalidate: "novalidate"}
		{* <h2>
			{_newsletter_title}
		</h2> *}

		<p class="f-newsletter__wrap">
			<label for="newsletterFormEmail" class="f-newsletter__label">Sign up for hot offers</label>
			<span class="f-newsletter__inp-wrap inp-fix input-group">
				<input n:name="email" required placeholder="{_newsletter_footer_placeholder}" id="newsletterFormEmail" class="f-newsletter__inp inp-text input-group__control inp-validate">
				<span class="input-group__addon">
					<button type="submit" class="f-newsletter__btn btn btn--bd btn--plus">
						<span class="btn__text">
							<span>
								{_"btn_footer_newsletter"} <span class="f-newsletter__btn-suffix">{_"btn_footer_newsletter_desktop_suffix"}</span>
							</span>
							{('plus')|icon, 'btn__icon'}
						</span>
					</button>
				</span>
			</span>
		</p>

		{control messageForForm, $flashes, $form, TRUE, ['timeToggle'=>true]}

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

		{if isset($flashes[0]) && $flashes[0]->type == "ok" && $flashes[0]->message == "newsletterAdded"}
			<script>
				dataLayer.push({
					'event' : 'action.optin.newsletter'
				});
			</script>
		{/if}
	{/form}
{/snippet}
