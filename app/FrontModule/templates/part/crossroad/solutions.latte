{default $class = 'u-mb-4xl'}
{default $cf = $object->cf->solutions ?? false}

<section n:if="$cf" n:class="c-solutions, $class">
	<div class="row-main">
		<h2 n:if="$cf->title ?? false" class="pp-18 u-title-line u-mb-md">{$cf->title|texy|noescape}</h2>
		<div n:if="count($cf->items ?? [])" class="grid">
			{foreach $cf->items as $item}
				{php $page = isset($item->page) ? $item->page->getEntity() ?? false : false}
				{php $img = isset($item->image) ? $item->image->getEntity() ?? false : false}
				<div n:if="$page" class="grid__cell size--6-12@md">
					<article class="c-solutions__item link-mask">
						<h3 class="h2">
							<img n:if="$img" class="c-solutions__img" src="{$img->getSize('xl')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
							<img n:if="!$img" class="c-solutions__img" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
							<a href="{plink $page}" class="link-mask__link">
								{$page->nameAnchor}
							</a>
						</h3>
						<p n:if="$item->annot ?? false" class="c-solutions__annot pp-24">
							{$item->annot}
						</p>
					</article>
				</div>
			{/foreach}
		</div>
	</div>
</section>