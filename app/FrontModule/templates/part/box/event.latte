{default $class = false}
{default $item = false}

<article n:if="$item" n:class="b-event, $class, link-mask">
	{php $dates = $item->cf->workshopDates ?? []}

	{php $status = 'ongoing'}
	{php $now = new DateTimeImmutable()}
	{php $now->format('Y-m-d H:i:s')}
	{php $first = $dates[0]}
	{php $last = $dates[count($dates)-1]}
	{php $firstFrom = $first->from ?? null}
	{php $lastTo = $last->to ?? $last->from}

	{if $firstFrom && $lastTo}
		{php $lastToPlus = $lastTo->modify('+1 day -1 second')}
		{if $now < $firstFrom}
			{php $status = 'not_started'}
		{elseif $now > $lastToPlus}
			{php $status = 'finished'}
		{/if}
	{/if}

	<p class="b-event__img-holder">
		{php $img = isset($item->workshop->cf->info->mainImage) ? $item->workshop->cf->info->mainImage->getEntity() ?? false : false}
		<img n:if="$img" class="img img--3-2" src="{$img->getSize('xl-3-2')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
		<img n:if="!$img" class="img img--3-2" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		<span class="b-event__status btn btn--inverse btn--sm">
			<span class="btn__text">
				{_"workshop_status_" . $status}
			</span>
		</span>
	</p>


	<h3 class="b-event__bottom u-mb-0 pp-18">
		<a href="{plink $item}" class="b-events__link link-mask__link">{$item->nameAnchor}</a>
		<span class="btn btn--bd btn--sm">
			<span class="btn__text">
				{include $templates.'/part/box/part/workshop-date.latte', dates: $dates}
			</span>
		</span>
	</h3>
</article>