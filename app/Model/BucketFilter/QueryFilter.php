<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\BucketFilter\ElasticItem\ElasticItem;
use App\Model\BucketFilter\ElasticItem\QuestionableElasticItem;
use App\Model\BucketFilter\ElasticItem\Range;
use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

final class QueryFilter
{

	public function newGet(array $filterItems, ?string $elasticKeyToIgnore = null, bool $ignoreRange = false): AbstractQuery
	{
		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();
		foreach ($filterItems as $filterItem) {
			if ($ignoreRange && $filterItem instanceof Range) {
				continue;
			}

			assert($filterItem instanceof ElasticItem);
			if ($elasticKeyToIgnore === $filterItem->getElasticKey()) {
				continue;
			}

			assert($filterItem instanceof QuestionableElasticItem);
			$condition = $filterItem->getCondition();
			if ($condition !== null) {
				$boolQuery->addMust($condition);
			}
		}

		return $boolQuery;
	}

}
