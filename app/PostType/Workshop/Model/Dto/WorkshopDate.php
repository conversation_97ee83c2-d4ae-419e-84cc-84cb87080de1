<?php declare(strict_types=1);

namespace App\PostType\Workshop\Model\Dto;

use DateTimeImmutable;

readonly class WorkshopDate
{
	public const string StatePreparation = 'preparation';
	public const string StateRunning = 'running';
	public const string StateFinished = 'finished';

	public function __construct(
		public DateTimeImmutable $from,
		public ?DateTimeImmutable $to,
		public string $lector,
		public string $state
	)
	{
	}

	public function getState(): string
	{
		return "workshop_state_$this->state";
	}

	public function isRunning(): bool
	{
		return $this->state === self::StateRunning;
	}
	public function isFinished(): bool
	{
		return $this->state === self::StateFinished;
	}
	public function isPreparation(): bool
	{
		return $this->state === self::StatePreparation;
	}

	public static function from(\stdClass $cf): self
	{
		return new self(
			$cf->from,
			$cf->to ?? null,
			$cf->lector ?? '',
			self::setState($cf->from, $cf->to ?? null)
		);
	}

	private static function setState(DateTimeImmutable $from, ?DateTimeImmutable $to): string
	{
		$now = new DateTimeImmutable();

		if ($now < $from) {
			return self::StatePreparation;
		}

		if ($now > $to) {
			return self::StateFinished;
		}

		return self::StateRunning;
	}
}
