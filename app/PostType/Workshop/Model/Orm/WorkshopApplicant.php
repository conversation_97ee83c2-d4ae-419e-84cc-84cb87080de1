<?php declare(strict_types=1);

namespace App\PostType\Workshop\Model\Orm;

use App\Model\Orm\BackedEnumWrapper;
use App\Model\Orm\BaseEntity;
use App\PostType\Workshop\Model\Enum\ApplicantExperience;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property string $name
 * @property string|null $bakery
 * @property string $email
 * @property string $phone
 * @property string $message
 *
 * @property ApplicantExperience $experience {wrapper BackedEnumWrapper}
 *
 * @property DateTimeImmutable $agreedAt {default now}
 * @property DateTimeImmutable $workshopFrom
 * @property DateTimeImmutable|null $workshopTo
 *
 * RELATIONS
 * @property Workshop $workshop {m:1 Workshop::$applicants}
 */
class WorkshopApplicant	extends BaseEntity
{
}
