<?php declare(strict_types = 1);

namespace App\PostType\Workshop\AdminModule\Components\DataGrid\WorkshopApplicant;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use Nextras\Orm\Collection\ICollection;

interface ApplicantDataGridFactory
{

	public function create(
		ICollection $collection,
		?DataGridDefinition $dataGridDefinition = null,
	): ApplicantDataGrid;

}
