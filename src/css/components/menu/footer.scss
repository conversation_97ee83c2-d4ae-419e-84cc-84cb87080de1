@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-footer {
	&__list {
		@extend %reset-ul;
		gap: var(--grid-gutter);
		column-count: 2;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-link-hover: #{variables.$color-link};
	}

	// MQ
	@media (config.$sm-up) {
		&__list {
			column-count: 3;
		}
	}
}
