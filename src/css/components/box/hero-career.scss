@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-hero-career {
	position: relative;
	background: variables.$color-black;
	color: variables.$color-cream;
	overflow: hidden;
	&::before {
		content: '';
		position: absolute;
		inset: 0;
		background: linear-gradient(358.92deg, rgba(0, 0, 0, 0) 86.14%, #000000 124.58%);
	}
	&__bg {
		position: absolute;
		inset: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
		transform: translateZ(0);
		will-change: transform;
	}
	.row-main {
		display: flex;
		flex-direction: column;
		justify-content: center;
		min-height: 100svh;
		aspect-ratio: 16/9;
		padding: clamp(12.5rem, calc(250 / 1920 * 100vw), 25rem) 0 var(--row-main-gutter);
	}
	&__inner {
		width: 100%;
	}
}
