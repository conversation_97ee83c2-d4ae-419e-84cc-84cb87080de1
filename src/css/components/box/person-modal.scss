@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-person-modal {
	width: 100%;
	max-width: none;
	height: 100%;
	max-height: none;
	margin: 0;
	padding: 0;
	border: 0;
	background: variables.$color-yeast;
	opacity: 1;
	transition: opacity 0.3s ease-in-out;

	&::backdrop {
		background-color: variables.$color-bg;
	}

	&__inner {
		height: 100%;
	}
	&__content {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: functions.dynamic-size(52);
		overflow-y: auto;
		img {
			aspect-ratio: auto;
		}
	}
	&__img-wrapper {
		position: relative;
		aspect-ratio: 1/1;
		img {
			position: absolute;
			inset: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
		}
	}
	&__modal-img-wrapper {
		align-self: flex-end;
	}

	&__close {
		position: absolute;
		top: functions.dynamic-size(52);
		right: functions.dynamic-size(52);
		display: flex;
		justify-content: center;
		align-items: center;
		width: functions.dynamic-size(40);
		height: functions.dynamic-size(40);
		padding: 0;
		border: 0;
		background: none;
		appearance: none;
		cursor: pointer;
		transition: color 0.15s ease-in-out;
		&:hover {
			color: variables.$color-hot;
		}

		svg {
			width: functions.dynamic-size(20);
			height: functions.dynamic-size(20);
		}
	}

	@media (config.$md-up) {
		max-width: calc(100vw - #{functions.dynamic-size(112)});
		max-height: calc(100vh - #{functions.dynamic-size(102)});
		margin: functions.dynamic-size(51) functions.dynamic-size(56);
		&__inner {
			display: grid;
			grid-template-columns: 1fr 1fr;
		}
		&__img-wrapper {
			height: 100%;
			aspect-ratio: auto;
		}
	}
	@media (config.$xxxl-up) {
		&__content {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: functions.dynamic-size(50);
		}
	}
	/* stylelint-disable-next-line scss/at-rule-no-unknown */
	@starting-style {
		opacity: 0;
	}
}
