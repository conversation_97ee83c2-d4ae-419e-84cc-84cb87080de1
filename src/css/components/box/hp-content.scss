@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/typography';
@use 'base/extends';
@use 'base/mixins';

.b-hp-content {
	overflow: clip;

	&__title {
		@extend %pp-18;
		flex: 0 0 100%;
		margin: 0;
		font-family: variables.$font-primary;
		font-weight: normal;
		text-transform: none;
	}

	&__annot {
		@extend %pp-48;
		flex: 0 0 100%;
		margin: 0;
		opacity: 0.1;
		transition: opacity 0.2s ease-in-out;
		&.is-snapped {
			opacity: 1;
		}
	}

	&__img {
		position: relative;
		flex: 0 0 100%;
		aspect-ratio: 16 / 9;
		overflow: hidden;
		opacity: 0.1;
		transition: opacity 0.2s ease-in-out;
		&.is-snapped {
			opacity: 1;
		}
		img {
			position: absolute;
			inset: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
		}
	}

	&__carousel-title-inner,
	&__carousel-content-inner,
	&__carousel-img-inner {
		display: flex;
	}

	&__carousel-title {
		margin-bottom: functions.spacing('xl');
	}

	&__carousel-content-inner {
		gap: calc(12.5% + 3 * var(--grid-gutter));
	}

	&__carousel-img {
		width: 50%;
		margin-top: auto;
		margin-left: auto;
	}
	&__carousel-img-inner {
		gap: var(--grid-gutter);
	}

	&__placeholder {
		height: 150svh;
	}

	&__sticky {
		position: sticky;
		top: 0;
		display: flex;
		flex-direction: column;
		min-height: 100svh;
		padding: functions.spacing('2xl') 0;
		+ .b-hp-content__placeholder {
			height: 50svh; // 100 from sticky, + 50 here for total 150, keep in sync with &__placeholder
		}
	}

	@media (config.$md-up) {
		&__carousel-img {
			width: 40%;
		}
	}

	@media (config.$lg-up) {
		&__carousel-content {
			margin-inline: calc(16.6666% + 2 * var(--grid-gutter));
		}
		&__carousel-img {
			width: 33.3333%;
			margin-inline: calc(16.6666% + 2 * var(--grid-gutter));
		}
	}
}
