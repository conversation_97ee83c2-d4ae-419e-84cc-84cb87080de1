@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'config';

.b-header {
	$s: &;
	position: fixed;
	inset: 0;
	z-index: 12;
	background: variables.$color-text;
	color: variables.$color-cream;
	opacity: 0;
	visibility: hidden;
	pointer-events: auto;
	transition: opacity 0.15s ease-in-out, visibility 0.15s 0.15s ease-in-out;

	a {
		--color-hover: #{variables.$color-hot};
		--color-hover-decoration: transparent;
		color: currentcolor;
	}

	.row-main {
		display: flex;
		align-items: flex-end;
		height: 100%;
		padding-top: 100px;
		padding-bottom: functions.dynamic-size(56);
		padding-bottom: 3.5rem;
		overflow-y: auto;
	}

	.is-open & {
		opacity: 1;
		visibility: visible;
		transition: opacity 0.15s ease-in-out, visibility 0.15s ease-in-out;
	}

	&__grid {
		display: flex;
		flex-direction: column;
		gap: 4rem;
		justify-content: space-between;
		align-items: flex-start;
		width: 100%;

		&:first-child {
			margin-top: auto;
		}

		@media (config.$md-up) {
			flex-direction: row;
			gap: functions.dynamic-size(120);
			align-items: flex-end;
		}
	}

	&__grid-cell--photo {
		width: clamp(20rem, calc(480 / 1920 * 100vw), 100rem);
	}

	&__image {
		position: relative;
		aspect-ratio: 483 / 628;
		&-img {
			position: absolute;
			inset: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
		}
	}

	&__nav-list {
		@extend %reset-ul;
	}
	&__nav-item {
		@extend %reset-ul-li;
		& + & {
			margin-top: 0.6rem;
		}
	}

	&__grid-cell--nav {
		display: flex;
		flex-direction: column;
		gap: 4rem;
		align-self: stretch;
		text-transform: uppercase;
		@media (config.$md-up) {
			gap: functions.dynamic-size(20);
		}
	}

	&__nav {
		font-family: variables.$font-secondary;
		font-weight: 700;
		font-size: functions.dynamic-size(48);
		line-height: 1.2;
		letter-spacing: -0.01em;
	}
	&__nav-secondary {
		columns: 2;
		column-gap: clamp(3rem, calc(100 / 1920 * 100vw), 10rem);
		margin-top: auto;
		text-transform: none;
	}

	&__grid-cell--lang {
		display: flex;
		flex-grow: 1;

		@media (config.$md-up) {
			justify-content: flex-end;
		}
	}
}
