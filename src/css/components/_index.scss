// Box
@forward 'components/box/annot';
@forward 'components/box/cookie';
@forward 'components/box/header';
@forward 'components/box/content';
@forward 'components/box/contact';
@forward 'components/box/contact-map';
@forward 'components/box/custom-select';
@forward 'components/box/downloads';
@forward 'components/box/about';
@forward 'components/box/hero-community';
@forward 'components/box/hero-roots';
@forward 'components/box/management';
@forward 'components/box/benefits';
@forward 'components/box/grid-gallery';
@forward 'components/box/img-content';
@forward 'components/box/event';
@forward 'components/box/reasons';
@forward 'components/box/story';
@forward 'components/box/lab';
@forward 'components/box/service';
@forward 'components/box/premium';
@forward 'components/box/hero-solutions';
@forward 'components/box/solutions-video';
@forward 'components/box/case-study';
@forward 'components/box/clients';
@forward 'components/box/hero-hp';
@forward 'components/box/tippy-box';
@forward 'components/box/hero-sustainability';
@forward 'components/box/numbers';
@forward 'components/box/scroll-switch-section';
@forward 'components/box/summary';
@forward 'components/box/career-gallery';
@forward 'components/box/team';
@forward 'components/box/hero-career';
@forward 'components/box/career-gallery-grid';
@forward 'components/box/contact-career';
@forward 'components/box/article-content';
@forward 'components/box/share';
@forward 'components/box/workshop-info';
@forward 'components/box/workshop-content';
@forward 'components/box/workshop-section';
@forward 'components/box/workshop-lectors';
@forward 'components/box/workshop-program';
@forward 'components/box/workshop-benefits';
@forward 'components/box/workshop-summary';
@forward 'components/box/components';
@forward 'components/box/product-detail';
@forward 'components/box/hp-content';
@forward 'components/box/products-content';
@forward 'components/box/person-modal';
@forward 'components/box/product';
// @forward 'components/box/modal';

// Crossroad
@forward 'components/crossroad/events';
@forward 'components/crossroad/categories';
@forward 'components/crossroad/solutions';
@forward 'components/crossroad/positions';
@forward 'components/crossroad/authors';
@forward 'components/crossroad/products-categories';
@forward 'components/crossroad/products';

// Form
@forward 'components/form/contact';
@forward 'components/form/join';
@forward 'components/form/newsletter';
@forward 'components/form/contact-full';

// Menu
@forward 'components/menu/accessibility';
@forward 'components/menu/main';
@forward 'components/menu/lang';
@forward 'components/menu/footer';
@forward 'components/menu/breadcrumb';
