@use 'config';
// @use 'base/functions';

:root {
	--grid-gutter: 2rem;
	--header-height: clamp(8.8rem, calc(104 / 1920 * 100vw), 10.4rem);
	--row-main-gutter: clamp(2rem, calc(56 / 1920 * 100vw), 5.6rem);
	--vw: calc(100vw - var(--scrollbar-width, 0rem));
	--row-main-width: calc(clamp(144rem, calc(1808 / 1920 * 100vw), 180.8rem) + 2 * var(--row-main-gutter));
}

// Colors
$color-crust: #321919;
$color-hot: #eb642d;
$color-yeast: #f0c382;
$color-cream: #f5f0e6;
$color-dark-cream: #e8e1d8;
$color-beige: #f4e6d980;
$color-white: #ffffff;
$gradient-bake: linear-gradient(90deg, #321404 0%, #eb642d 48.56%, #eaa383 74.28%, #e8e1d8 100%);

$color-primary: $color-crust;
$color-secondary: $color-hot;

$color-black: $color-crust;
// $color-white: #ffffff;
$color-red: #ff0000;
$color-green: #008800;
$color-orange: #ffaa00;
$color-gray: #808080;

$color-text: $color-black;
$color-bd: $color-crust;
$color-bg: $color-cream;
$color-link: $color-text;
$color-hover: $color-text;

// $color-facebook: #3b5998;
// $color-twitter: #1da1f2;
// $color-google: #dd4b39;
// $color-youtube: #ff0000;
// $color-linkedin: #0077b5;
// $color-instagram: #c13584;
// $color-pinterest: #bd081c;

// Font
$font-system: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, helvetica, arial, sans-serif;
$font-primary: 'PP Neue Montreal', $font-system;
$font-secondary: 'Soehne Breit', $font-primary;
$font-size: clamp(1.35rem, calc(18 / 1920 * 100vw), 1.8rem);
// $font-size: functions.dynamic-size(18);
$line-height: calc(24 / 18);

// Typography
$typo-space-vertical: 1lh;

// Focus
$focus-outline-color: $color-primary;
$focus-outline-style: solid;
$focus-outline-width: 0.1rem;

// Spacing
$utils-spacing: (
	'0': 0,
	'xs': clamp(0.8rem, calc(12 / 1920 * 100vw), 1.2rem),
	'sm': clamp(1.2rem, calc(24 / 1920 * 100vw), 2.4rem),
	'md': clamp(2.4rem, calc(48 / 1920 * 100vw), 4.8rem),
	'lg': clamp(4.8rem, calc(56 / 1920 * 100vw), 5.6rem),
	'xl': clamp(5.6rem, calc(84 / 1920 * 100vw), 8.4rem),
	'2xl': clamp(8.4rem, calc(120 / 1920 * 100vw), 12rem),
	'3xl': clamp(10rem, calc(158 / 1920 * 100vw), 15.8rem),
	'4xl': clamp(10rem, calc(300 / 1920 * 100vw), 30rem)
);

// Spacing function moved here
@function spacing($depth: '0') {
	@return map-get($utils-spacing, $depth);
}

// Grid
$grid-columns: 12;
$grid-gutter: var(--grid-gutter);
$row-main-width: var(--row-main-width);
$row-main-gutter: var(--row-main-gutter);

// Paths
$img-path: map-get(config.$paths, 'images');
$fonts-path: map-get(config.$paths, 'fonts');

// Transitions
$t: 0.3s;

// SVGs
$svg-bullet: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath d='M0 0h4v4H0z'/%3E%3C/svg%3E%0A";
$svg-select: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 10'%3E%3Cpath fill='currentColor' d='M7.229 9.34a1 1 0 0 1-.733-.32l-6.5-7L1.461.66 7.229 6.87 12.996.66l1.465 1.362-6.5 7-.074.072a1 1 0 0 1-.658.247Z'/%3E%3C/svg%3E";
$svg-arrow: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2020%2017%22%3E%0A%20%20%3Cpath%20fill%3D%22%23321919%22%20d%3D%22M20%207.697a14.524%2014.524%200%200%201-4.72-3.023%2013.818%2013.818%200%200%201-3.144-4.51l-1.57.679a15.538%2015.538%200%200%200%203.53%205.066c.666.639%201.38%201.215%202.141%201.728H0v1.71h16.262a16.285%2016.285%200%200%200-2.166%201.743%2015.507%2015.507%200%200%200-3.53%205.068l1.57.678a13.824%2013.824%200%200%201%203.144-4.51A14.572%2014.572%200%200%201%2020%209.303l-.32-.803.32-.803Z%22%2F%3E%0A%3C%2Fsvg%3E';
