@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.embla {
	--arrow-position: 0.5rem;
	--hover-offset-x: var(--row-main-gutter);
	--hover-offset-y: 15rem;
	$s: &;
	position: relative;
	overflow: hidden;
	pointer-events: none;
	&:has(&__holder) {
		overflow: visible;
	}
	&__holder:first-child {
		margin: calc(var(--hover-offset-y) * -1) calc(var(--hover-offset-x) * -1);
		overflow: hidden;
	}
	&__viewport {
		width: 100%;
		pointer-events: none;
		&.is-draggable {
			cursor: move;
			cursor: grab;
		}
		&.is-dragging {
			cursor: grabbing;
			pointer-events: none;
		}
		& > * {
			pointer-events: auto;
		}
	}
	&__holder &__viewport {
		padding: var(--hover-offset-y) var(--hover-offset-x);
	}
	&__container {
		transform: translateZ(0);
		will-change: transform;
		.js &.grid--scroll {
			overflow: visible;
		}
	}
	&__btn {
		@include mixins.button-reset;
		position: absolute;
		top: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 4rem;
		height: 4rem;
		border-radius: 50%;
		background: variables.$color-primary;
		color: variables.$color-white;
		cursor: pointer;
		pointer-events: auto;
		transition: opacity variables.$t, visibility variables.$t, background-color variables.$t, color variables.$t;
		transform: translateY(-50%);
		&[disabled] {
			opacity: 0;
			visibility: hidden;
		}
		.icon-svg {
			width: 1.4rem;
			height: 1.4rem;
		}
	}
	&__dots {
		--dots-top: 1rem;
		--dots-gap: 0.8rem;
		--dot-size: 0.8rem;
		display: flex;
		justify-content: center;
		margin-bottom: calc(var(--dots-gap) / -2);
		padding-top: calc(var(--dots-top) - var(--dots-gap) / 2);
		font-size: 0;
		pointer-events: auto;
	}
	&__dot {
		@include mixins.button-reset;
		position: relative;
		width: calc(var(--dot-size) + var(--dots-gap));
		height: calc(var(--dot-size) + var(--dots-gap));
		font-size: 0;
		cursor: pointer;
		&::before {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: var(--dot-size);
			height: var(--dot-size);
			border-radius: 50%;
			background: variables.$color-cream;
			transition: background-color variables.$t;
			transform: translate(-50%, -50%);
		}
	}

	// VARIANTs
	&__btn--prev {
		left: var(--arrow-position);
	}
	&__btn--next {
		right: var(--arrow-position);
	}

	// STATES
	&__container.grid--center {
		justify-content: flex-start;
		& > *:first-child {
			margin-left: auto;
		}
		& > *:last-child {
			margin-right: auto;
		}
	}
	&__dot.is-selected {
		&::before {
			background: variables.$color-secondary;
		}
	}
	&__btn:focus,
	.hoverevents &__btn:focus:hover,
	.hoverevents &__dot:hover::before {
		background: variables.$color-link;
	}
	.no-js &__btn,
	&:not(.is-initialized) &__btn,
	&.is-disabled &__btn {
		display: none;
	}

	// HOVERs
	.hoverevents &__btn:hover {
		background: variables.$color-secondary;
		color: variables.$color-white;
	}

	// MQ
	@media (config.$md-up) {
		--arrow-position: 2rem;
		&__btn {
			width: 5.6rem;
			height: 5.6rem;
			.icon-svg {
				width: 2rem;
				height: 2rem;
			}
		}
	}
}
